import json
import pandas as pd
import torch
import os
import re

def build_knowledge_tree_and_convert_json(excel_path, csv_path, output_path):
    """
    构建知识树并转换CSV格式
    
    参数:
        excel_path: 知识树Excel文件路径
        csv_path: 输入的CSV文件路径
        output_path: 输出的JSON文件路径
    """
    # 读取Excel构建知识树
    df_tree = pd.read_excel(excel_path)
    nodes = {}
    root_nodes = []
    
    # 构建节点字典
    for _, row in df_tree.iterrows():
        node = {
            'id': str(row['ID']),
            'name': row['名称'],
            'parent_id': str(row['父级ID']),
            'level': row['节点层级'],
            'children': []
        }
        nodes[str(row['ID'])] = node
    
    # 构建树结构
    for node in nodes.values():
        # if pd.isna(node['parent_id']):  # 根节点
        if node['parent_id'] == "nan":  # 根节点
            root_nodes.append(node)
        else:  # 子节点
            parent = nodes.get(node['parent_id'])
            if parent:
                parent['children'].append(node)

    # 读取CSV文件
    df_csv = pd.read_csv(csv_path)
    
    result = []
    for _, row in df_csv.iterrows():
        # 获取清洗后的题干和知识点名称
        clean_text = row['html清洗题干']
        knowledge_names = [name.strip() for name in str(row['知识点名称']).split(';') if name.strip()]
        
        labels = []
        for name in knowledge_names:
            # 查找节点
            for node in nodes.values():
                if node['name'] == name:
                    # 获取从当前节点到根节点的完整路径
                    path = []
                    current = node
                    while current:
                        path.insert(0, current['name'])
                        current = nodes.get(current['parent_id'])
                    
                    # 如果是叶子节点，直接使用完整路径
                    if not node['children']:
                        labels.extend(path)
                    else:
                        # 如果不是叶子节点，获取最左侧路径上的所有节点
                        def get_left_path(current_node):
                            path = [current_node['name']]
                            if current_node['children']:
                                path.extend(get_left_path(current_node['children'][0]))
                            return path
                        
                        left_path = get_left_path(node)
                        # 组合完整路径和最左侧路径
                        labels.extend(path + left_path[1:])  # 跳过重复的当前节点
                    break
        
        if labels:
            result.append({
                'doc_token': clean_text,
                'doc_label': labels
            })
    
    # 保存结果
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"转换完成，结果已保存到 {output_path}")

def build_knowledge_tree_and_convert_json_by_id(excel_path, csv_path, output_path):
    """
    构建知识树并转换CSV格式 根据id转换为json
    
    参数:
        excel_path: 知识树Excel文件路径
        csv_path: 输入的CSV文件路径
        output_path: 输出的JSON文件路径
    """
    # 读取Excel构建知识树
    df_tree = pd.read_excel(excel_path)
    nodes = {}
    root_nodes = []
    
    # 构建节点字典
    for _, row in df_tree.iterrows():
        node = {
            'id': str(row['ID']),
            'name': row['名称'],
            'parent_id': str(row['父级ID']),
            'level': row['节点类型'],
            'children': []
        }
        nodes[str(row['ID'])] = node
    
    # 构建树结构
    for node in nodes.values():
        # if pd.isna(node['parent_id']):  # 根节点
        if node['parent_id'] == "nan":  # 根节点
            root_nodes.append(node)
        else:  # 子节点
            parent = nodes.get(node['parent_id'])
            if parent:
                parent['children'].append(node)

    # 读取CSV文件
    df_csv = pd.read_excel(csv_path)
    
    result = []
    for _, row in df_csv.iterrows():
        # 获取清洗后的题干和知识点名称
        clean_text = row['html清洗题干']
        knowledge_ids = [name.strip() for name in str(row['知识点ID']).split(';') if name.strip()]
        
        labels = []
        for kid in knowledge_ids:
            # 查找节点
            for node in nodes.values():
                if node['id'] == kid:
                    # 获取从当前节点到根节点的完整路径
                    path = []
                    current = node
                    while current:
                        path.insert(0, current['name'])
                        current = nodes.get(current['parent_id'])
                    
                    # 如果是叶子节点，直接使用完整路径
                    if not node['children']:
                        labels.extend(path)
                    else:
                        # 如果不是叶子节点，获取最左侧路径上的所有节点
                        def get_left_path(current_node):
                            path = [current_node['name']]
                            if current_node['children']:
                                path.extend(get_left_path(current_node['children'][0]))
                            return path
                        
                        left_path = get_left_path(node)
                        # 组合完整路径和最左侧路径
                        labels.extend(path + left_path[1:])  # 跳过重复的当前节点
                    break
        
        if labels:
            result.append({
                'doc_token': clean_text,
                'doc_label': labels
            })
    
    # 保存结果
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"转换完成，结果已保存到 {output_path}")


def save_knowledge_by_level(excel_path, output_dir):
    """
    按层级保存知识点到不同文件
    
    参数:
        excel_path: 知识树Excel文件路径
        output_dir: 输出目录路径
    """
    # 读取Excel构建知识树
    df = pd.read_excel(excel_path)
    level_nodes = {}
    leaf_nodes = []
    
    # 按层级分类知识点
    for _, row in df.iterrows():
        # level = row['节点类型']
        level = row['节点层级']
        if pd.isna(level):
            continue
            
        if level not in level_nodes:
            level_nodes[level] = []
        level_nodes[level].append(row['ID'])
        # level_nodes[level].append(row['名称'])
        # 判断是否为叶子节点(没有子节点的节点)
        if row['ID'] not in df['父级ID'].values:
            leaf_nodes.append((row['名称']))
        # if row['名称'] not in df['父级名称'].values:
        #     leaf_nodes.append((row['名称']))
    
    # 保存到不同文件
    import os
    os.makedirs(output_dir, exist_ok=True)  # 确保目录存在
    all_labels = []
    for level, names in level_nodes.items():
        # 移除"级知识点"等非数字字符，只保留数字
        level_num = ''.join(filter(str.isdigit, str(level)))
        output_path = f"{output_dir}label{level_num}.txt"
        with open(output_path, 'w', encoding='utf-8') as f:
            for name in names:
                f.write(f"{name}\n")
                all_labels.append(name)  # 添加到所有标签列表
    
    # 新增：保存所有标签到一个文件
    with open(f"{output_dir}all_label.txt", 'w', encoding='utf-8') as f:
        for label in all_labels:
            f.write(f"{label}\n")

    with open(f"{output_dir}label_leaf.txt", 'w', encoding='utf-8') as f:
        for name in leaf_nodes:
            f.write(f"{name}\n")
    
    print(f"知识点已按层级保存到 {output_dir}")


def build_level_mappings(excel_path, label_dir, output_dir):
    """
    构建知识树层级映射关系并保存为pt文件

    参数:
        excel_path: 知识树Excel文件路径
        label_dir: 包含labelN.txt的目录路径
        output_dir: 输出目录路径
    """
    # 1. 读取Excel文件构建知识树
    df = pd.read_excel(excel_path)

    # 2. 动态获取所有labelN.txt的N
    label_files = [f for f in os.listdir(label_dir) if re.match(r'label\d+\.txt$', f)]
    levels = sorted([int(re.findall(r'\d+', f)[0]) for f in label_files])

    # 3. 读取各层级label文件并构建名称到索引的映射
    level_to_names = {}
    for level in levels:
        label_file = os.path.join(label_dir, f'label{level}.txt')
        with open(label_file, 'r', encoding='utf-8') as f:
            names = [line.strip() for line in f if line.strip()]
            level_to_names[level] = names

    # 4. 构建层级映射关系
    mappings = {}

    parent_to_children = defaultdict(list)
    for _, row in df.iterrows():
        if pd.notna(row['父级ID']):
            parent_to_children[str(row['父级ID'])].append(row)

    for i in range(len(levels) - 1):
        parent_level = levels[i]
        child_level = levels[i + 1]
        if parent_level not in level_to_names or child_level not in level_to_names:
            continue

        mapping = defaultdict(list)
        parent_names = level_to_names[parent_level]
        child_names = level_to_names[child_level]

        parent_id_to_idx = {kid: idx for idx, kid in enumerate(parent_names)}
        child_id_to_idx = {kid: idx for idx, kid in enumerate(child_names)}

        for parent_id in parent_names:
            parent_idx = parent_id_to_idx.get(parent_id)
            if parent_idx is None:
                continue
            for child_row in parent_to_children.get(parent_id, []):
                child_id = str(child_row['ID'])
                if child_id in child_id_to_idx:
                    child_idx = child_id_to_idx[child_id]
                    mapping[parent_idx].append(child_idx)

        if mapping:
            mappings[f"level{parent_level}_to_level{child_level}"] = dict(mapping)

    # 5. 保存为pt文件和json文件
    os.makedirs(output_dir, exist_ok=True)
    for name, mapping in mappings.items():
        torch.save(mapping, os.path.join(output_dir, f"{name}.pt"))
        with open(os.path.join(output_dir, f"{name}.json"), 'w', encoding='utf-8') as f:
            json.dump(mapping, f, ensure_ascii=False, indent=2)

    print(f"层级映射关系已保存到 {output_dir}，共生成{len(mappings)}个pt文件")


def complete_knowledge_tree(input_excel, output_excel):
    """
    构建知识树并补全到最深层
    
    参数:
        input_excel: 输入Excel文件路径
        output_excel: 输出Excel文件路径
    """
    # 读取Excel文件
    df = pd.read_excel(input_excel)
    
    # 构建树结构
    nodes = {}
    root_nodes = []
    for _, row in df.iterrows():
        node = {
            'id': row['ID'],
            'name': row['名称'].strip(),  # 去除前后空格
            'parent_id': row['父级ID'],
            'level': row['节点类型'],
            'children': []
        }
        nodes[row['ID']] = node
    
    for node in nodes.values():
        if pd.isna(node['parent_id']):
            root_nodes.append(node)
        else:
            parent = nodes.get(node['parent_id'])
            if parent:
                parent['children'].append(node)

    # for _, row in df.iterrows():
    #     node = {
    #         'name': row['名称'].strip(),  # 去除前后空格
    #         'parent_name': row['父级名称'],
    #         'level': str(row['节点层级'])+"级知识点",
    #         'children': []
    #     }
    #     nodes[row['名称']] = node
    # 构建父子关系
    # for node in nodes.values():
    #     if pd.isna(node['parent_name']):
    #         root_nodes.append(node)
    #     else:
    #         parent = nodes.get(node['parent_name'])
    #         if parent:
    #             parent['children'].append(node)
    
    # 计算最大深度
    def get_max_depth(node):
        if not node['children']:
            return 1
        return 1 + max(get_max_depth(child) for child in node['children'])
    
    max_depth = max(get_max_depth(node) for node in root_nodes) if root_nodes else 0
    
    # 补全树结构到最深层
    def complete_tree(node, current_depth):
        global knowledge_num
        if current_depth < max_depth and not node['children']:
        # if current_depth <= max_depth and not node['children']:
            new_id = f"{node['id']}_auto_{current_depth}"
            node['children'].append({
                'id': new_id,
                'name': '补充知识点'+str(knowledge_num),
                # 'parent_name': node['name'],
                'parent_id': node['id'],
                'level': f"{current_depth}级知识点",
                'children': []
            })
            knowledge_num+=1
        for child in node['children']:
            complete_tree(child, current_depth + 1)
    
    for root in root_nodes:
        complete_tree(root, 1)
    
    # 将树结构转换回DataFrame
    def tree_to_df(node, rows):
        rows.append({
            # '名称': node['name'],
            # '父级名称': node['parent_name'],
            # '节点层级': node['level']
            'ID': node['id'],
            '父级ID': node['parent_id'],
            '名称': node['name'],
            '节点层级': node['level']
        })
        for child in node['children']:
            tree_to_df(child, rows)
    
    new_rows = []
    for root in root_nodes:
        tree_to_df(root, new_rows)
    
    # 保存到新Excel文件
    new_df = pd.DataFrame(new_rows)
    new_df.to_excel(output_excel, index=False)
    print(f"知识树已补全到最深层并保存到 {output_excel}")

def build_level_index_mappings1(excel_path, label_file_path, output_dir):
    """
    构建知识树层级索引映射并保存为pt文件
    
    参数:
        excel_path: 知识树Excel文件路径
        label_file_path: all_label.txt文件路径
        output_dir: 输出目录路径
    """
    # 1. 读取Excel文件构建知识树
    df = pd.read_excel(excel_path)
    
    # 2. 构建节点ID到索引的映射字典
    id_to_index = {}
    with open(label_file_path, 'r', encoding='utf-8') as f:
        for idx, line in enumerate(f):
            kid = line.strip()
            if kid:
                # 确保比较时类型一致
                # matching_nodes = df[df['名称'].astype(str) == str(kid)]
                matching_nodes = df[df['ID'].astype(str) == str(kid)]
                if not matching_nodes.empty:
                    id_to_index[kid] = idx
                else:
                    print(f"Warning: ID {kid} not found in Excel.")

    # 3. 构建知识树结构
    nodes = {}
    root_nodes = []
    max_level = 0
    
    for _, row in df.iterrows():
        node = {
            'id': str(row['ID']),
            'name': row['名称'],
            'parent_id': str(row['父级ID']),
            'level': row['节点类型'],
            'children': []
        }
        nodes[str(row['ID'])] = node
        # level_num = int(''.join(filter(str.isdigit, str(row['节点类型']))))
        level_str = ''.join(filter(str.isdigit, str(row['节点类型'])))
        level_num = int(level_str) if level_str else 0 
        if level_num > max_level:
            max_level = level_num
    
    for node in nodes.values():
        if pd.isna(node['parent_id']):
            root_nodes.append(node)
        else:
            parent = nodes.get(node['parent_id'])
            if parent:
                parent['children'].append(node)

    # 4. 构建层级映射关系
    mappings = {}
    all_level_mapping = {}  # 新增：存储所有层级映射
    
    for level in range(0, max_level):
        parent_level = level
        child_level = level + 1
        
        level_nodes = [n for n in nodes.values() 
                      if str(parent_level) in str(n['level'])]
        
        mapping = {}
        for parent_node in level_nodes:

            parent_idx = id_to_index.get(str(parent_node['id']), -1)
                
            child_indices = []
            for child in parent_node['children']:
                child_idx = id_to_index.get(str(child['id']), -1)
                if child_idx != -1:
                    child_indices.append(child_idx)
            
            if child_indices:
                mapping[parent_idx] = child_indices
        
        if mapping:
            key = f"level{parent_level}_to_level{child_level}"
            mappings[key] = mapping
            all_level_mapping[key] = mapping  # 添加到综合映射
    
    # 5. 保存为pt文件
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存各层级单独文件
    for name, mapping in mappings.items():
        torch.save(mapping, os.path.join(output_dir, f"{name}.pt"))

    # 保存各层级单独文件
    # for name, mapping in mappings.items():
    #     with open(os.path.join(output_dir, f"{name}.json"), 'w', encoding='utf-8') as f:
    #         json.dump(mapping, f, ensure_ascii=False, indent=2)
    
    # # 新增：保存所有层级映射的综合文件
    # if all_level_mapping:
    #     torch.save(all_level_mapping, os.path.join(output_dir, "all_level_mappings.pt"))
    
    print(f"层级索引映射已保存到 {output_dir}，包含{len(mappings)}个层级映射和1个综合映射文件")

def build_level_index_mappings(excel_path, label_file_path, output_dir):
    """
    构建知识树层级索引映射并保存为pt文件（基于名称构建树结构）

    参数:
        excel_path: 知识树Excel文件路径
        label_file_path: all_label.txt文件路径（名称列表）
        output_dir: 输出目录路径
    """
    # 1. 读取Excel文件构建知识树
    df = pd.read_excel(excel_path)
    df.fillna('', inplace=True)

    # 2. 构建名称到索引的映射
    name_to_index = {}
    with open(label_file_path, 'r', encoding='utf-8') as f:
        for idx, line in enumerate(f):
            name = line.strip()
            if name:
                matching = df[df['名称'].astype(str).str.strip() == name]
                if not matching.empty:
                    name_to_index[name] = idx
                else:
                    print(f"⚠️ Warning: 名称 \"{name}\" 未在 Excel 中找到。")

    # 3. 构建树节点结构（基于“名称”和“父级名称”）
    nodes = {}
    max_level = 0

    for _, row in df.iterrows():
        node_name = row['名称'].strip()
        parent_name = row['父级名称'].strip() if row['父级名称'] else ''
        level_str = str(row['节点层级']).strip()
        level_num = int(''.join(filter(str.isdigit, level_str))) if level_str else 0
        if level_num > max_level:
            max_level = level_num

        node = {
            'name': node_name,
            'parent_name': parent_name,
            'level': f"{level_num}级知识点",
            'level_num': level_num,
            'children': []
        }
        nodes[node_name] = node

    # 4. 构建父子关系
    for node in nodes.values():
        parent = nodes.get(node['parent_name'])
        if parent:
            parent['children'].append(node)

    # 5. 构建层级映射
    mappings = {}
    all_level_mapping = {}

    for level in range(0, max_level):
        parent_level = level
        child_level = level + 1

        # 找到所有处于 parent_level 的节点
        level_nodes = [n for n in nodes.values() if n['level_num'] == parent_level]

        mapping = {}
        for parent_node in level_nodes:
            parent_name = parent_node['name']
            parent_idx = name_to_index.get(parent_name, -1)

            child_indices = []
            for child in parent_node['children']:
                child_idx = name_to_index.get(child['name'], -1)
                if child_idx != -1:
                    child_indices.append(child_idx)

            if child_indices:
                mapping[parent_idx] = child_indices

        if mapping:
            key = f"level{parent_level}_to_level{child_level}"
            mappings[key] = mapping
            all_level_mapping[key] = mapping

    # 6. 保存 pt 文件
    os.makedirs(output_dir, exist_ok=True)

    for name, mapping in mappings.items():
        torch.save(mapping, os.path.join(output_dir, f"{name}.pt"))

    # 保存所有层级合并文件（可选）
    torch.save(all_level_mapping, os.path.join(output_dir, "all_level_mappings.pt"))

    print(f"✅ 层级索引映射已保存到 {output_dir}，共生成 {len(mappings)} 个映射")





def build_nested_mapping(excel_path, label_file_path, output_path):
    """
    构建嵌套字典格式的层级映射并保存为JSON文件
    
    参数:
        excel_path: 知识树Excel文件路径
        label_file_path: all_label.txt文件路径
        output_path: 输出JSON文件路径
    """
    # 读取Excel文件
    df = pd.read_excel(excel_path)
    
    # 构建节点ID到索引的映射字典
    id_to_index = {}
    with open(label_file_path, 'r', encoding='utf-8') as f:
        for idx, line in enumerate(f):
            kid = line.strip()
            if kid:
                id_to_index[kid] = idx

    # 构建嵌套映射
    nested_mapping = {}
    
    # 按层级构建映射
    for _, row in df.iterrows():
        node_id = row['ID']
        parent_id = row['父级ID']
        level = row['节点类型']
        
        if pd.isna(parent_id):  # 根节点
            nested_mapping[id_to_index[node_id]] = {}
        else:
            # 查找父节点并建立嵌套关系
            current = nested_mapping
            parent_found = False
            
            # 递归查找父节点位置
            def find_parent(mapping, target_id):
                for k, v in mapping.items():
                    if k == id_to_index.get(target_id, -1):
                        return v
                    if isinstance(v, dict):
                        result = find_parent(v, target_id)
                        if result is not None:
                            return result
                return None
            
            parent_dict = find_parent(nested_mapping, parent_id)
            if parent_dict is not None:
                parent_dict[id_to_index[node_id]] = {}

    # 添加叶子节点数据
    for _, row in df.iterrows():
        node_id = row['ID']
        if not df[df['父级ID'] == node_id].empty:
            continue  # 不是叶子节点
            
        # 查找该节点的位置并添加数据
        current = nested_mapping
        path = []
        temp_id = node_id
        while True:
            parent_row = df[df['ID'] == temp_id]
            if parent_row.empty:
                break
            parent_id = parent_row.iloc[0]['父级ID']
            path.insert(0, temp_id)
            temp_id = parent_id
            if pd.isna(parent_id):
                break
                
        # 根据路径找到位置
        for node in path[:-1]:
            current = current.get(id_to_index.get(node, -1), {})
            
        # 添加叶子节点数据
        leaf_id = path[-1]
        current[id_to_index.get(leaf_id, -1)] = [id_to_index.get(leaf_id, -1)]

    # 保存为JSON文件
    # with open(output_path, 'w', encoding='utf-8') as f:
    #     json.dump(nested_mapping, f, ensure_ascii=False, indent=2)
    torch.save(nested_mapping, output_path)
    print(f"嵌套映射已保存到 {output_path}")



def match_level5_with_csv(excel_path, csv_path,output_txt_path):
    """
    匹配5级知识点与CSV文件中的名称，返回匹配的ID列表
    
    参数:
        excel_path: 知识树Excel文件路径
        csv_path: CSV文件路径
        
    返回:
        匹配的ID列表
    """
    # 读取Excel文件，筛选5级知识点
    df_excel = pd.read_excel(excel_path)
    level5_data = df_excel[df_excel['节点类型'] == '5级知识点']
    
    # 读取CSV文件
    df_csv = pd.read_csv(csv_path)
    
    matched_data = []  # 改为存储字典列表
    
    # 遍历5级知识点，查找匹配的CSV记录
    for _, row in level5_data.iterrows():
        name = row['名称']
        # 在CSV中查找名称匹配的记录
        matched = df_csv[df_csv['name'] == name]
        if not matched.empty:
            for _, match_row in matched.iterrows():
                matched_data.append({
                    'id': match_row['id'],
                    'name': name
                })
    
    with open(output_txt_path, 'w', encoding='utf-8') as f:
        for item in matched_data:
            f.write(f"{item['id']},{item['name']}\n")  # 用逗号分隔ID和名称
    
    print(f"匹配的ID和知识点名称已保存到 {output_txt_path}")


def convert_csv_to_json(csv_path, output_json_path1,output_json_path2, question_column='原始题干'):
    """
    将CSV文件中的题干列转换为指定JSON格式
    
    参数:
        csv_path: CSV文件路径
        output_json_path: 输出JSON文件路径
        question_column: 题干列名，默认为'题干'
    """
    # 导入HTML清洗所需模块
    import html_utils as hu
    
    # 读取CSV文件
    df = pd.read_csv(csv_path)
    
    # 构建JSON数据
    result = []
    result1 = []
    all_question = []
    for _, row in df.iterrows():
        if question_column in row:
            # 清洗HTML标签
            text = str(row[question_column])
            clean_text = hu.clean_html(text,False,True,False)
            # clean_text = BeautifulSoup(text, "html.parser").get_text()
            clean_text = annotate_numbers(clean_text)
            if clean_text.strip() == "" or len(clean_text)<10 or clean_text in all_question:  # 检查清洗后的文本是否为空
                continue  # 如果为空，跳过当前行
            all_question.append(clean_text)
            result.append({
                "doc_token": clean_text,  # 使用清洗后的文本
                "doc_label": []  # 空标签列表
            })
            ss = [kp.strip() for kp in str(row['知识点名称']).split(';') if kp.strip()]
            
            result1.append({
                "doc_token": clean_text,  # 使用清洗后的文本
                "doc_label": ss
            })
    
    # 保存为JSON文件
    with open(output_json_path1, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    with open(output_json_path2, 'w', encoding='utf-8') as f:
        json.dump(result1, f, ensure_ascii=False, indent=2)
    
    print(f"转换完成，结果已保存到文件")


def analyze_label_predictions(excel_path1, excel_path2):
    """
    分析标签预测结果并构建完整路径
    
    参数:
        excel_path1: 包含知识树的Excel文件路径
        excel_path2: 包含预测结果的Excel文件路径(结果也将保存到此文件)
    """
    # 1. 读取知识树并构建节点映射
    df_tree = pd.read_excel(excel_path1, sheet_name='Sheet1')
    nodes = {row['ID']: {'name': row['名称'], 'parent_id': row['父级ID'], 'level': row['节点类型']} 
             for _, row in df_tree.iterrows()}
    
    # 2. 读取预测结果
    df_pred = pd.read_excel(excel_path2, sheet_name='Sample_Contents')  # 假设第三分页名为Sheet3
    
    # 3. 为每个标签构建完整路径
    def get_full_path(node_id):
        path = []
        current = nodes.get(node_id)
        while current:
            path.insert(0, f"{current['name']}")
            current = nodes.get(current['parent_id'])
        return ' -> '.join(path)
    
    # 4. 添加路径列
    df_pred['True_Full_Path'] = df_pred['True_Label_Name'].apply(
        lambda x: get_full_path(next((k for k, v in nodes.items() if v['name'] == x), None)))
    df_pred['Pred_Full_Path'] = df_pred['Pred_Label_Name'].apply(
        lambda x: get_full_path(next((k for k, v in nodes.items() if v['name'] == x), None)))
    
    # 5. 保存结果到原Excel文件的新sheet中
    with pd.ExcelWriter(excel_path2, engine='openpyxl', mode='a') as writer:  
        df_pred.to_excel(writer, sheet_name='分析结果', index=False)
    
    print(f"分析结果已保存到 {excel_path2} 的'分析结果'工作表中")


def analyze_knowledge_point_distribution(excel_path):
    """
    统计各学科中题目数量超过100的知识点占比
    
    参数:
        excel_path: 包含知识点题目统计的Excel文件路径
        
    返回:
        各学科统计结果的DataFrame
    """
    # 读取Excel文件
    df = pd.read_excel(excel_path)
    
    # 按学科分组统计
    results = []
    for subject, group in df.groupby('subject_id'):
        total_points = len(group)  # 总知识点数
        over_100_points = len(group[group['question_count'] > 100])  # 题目数>100的知识点数
        
        # 计算占比
        if total_points > 0:
            percentage = (over_100_points / total_points) * 100
        else:
            percentage = 0
            
        results.append({
            '学科': subject,
            '总知识点数': total_points,
            '题目数>100的知识点数': over_100_points,
            '占比(%)': round(percentage, 2)
        })
    
    # 转换为DataFrame并返回
    result_df = pd.DataFrame(results)
    # 转换为DataFrame并保存到第二分页
    result_df = pd.DataFrame(results)
    with pd.ExcelWriter(excel_path) as writer:
        result_df.to_excel(writer, sheet_name='统计结果', index=False)
    # result.to_excel('d:\\菁优数据处理\\知识点分布统计结果.xlsx', index=False)
    # return result_df

def annotate_numbers(text):
    # 处理百分数（优先处理，避免与小数冲突）
    text = re.sub(r'(\d+%)', r'\1(百分数)', text)
    # 处理分数
    # text = re.sub(r'(\d+/\d+)', r'\1(分数)', text)
    text = re.sub(r'\\frac\{(\d+)\}\{(\d+)\}', r'\1/\2(分数)', text)
    # 处理小数（使用更精确的正则表达式）
    text = re.sub(r'(\d+\.\d+)(?![\.%\d])', r'\1(小数)', text)
    # 处理整数（最后处理，避免与其他模式冲突）
    if "(小数)" in text or "(分数)" in text:
        return text
    else:
        text = re.sub(r'(\d+)(?![\.%\d])', 
                        lambda m: f"{m.group(1)}(个位数、整数)" if len(m.group(1)) < 2 else 
                        f"{m.group(1)}({get_large_number_desc(m.group(1))})", text)
    return text
    
def get_large_number_desc(num_str):
    length = len(num_str)
    if length <= 2:
        return "百以下整数、二位数"
    elif length <= 3:
        return "千以下整数、三位数"
    elif length <= 4:
        return "万以下整数、四位数"
    elif length <= 5:
        return "万数、亿以内整数、五位数"
    elif length <= 6:
        return "十万数、亿以内数、六位数"
    elif length <= 7:
        return "百万数、亿以内整数、七位数"
    elif length <= 8:
        return "千万数、亿以内整数、八位数"
    else:
        return "亿以上整数"

def convert_csv_to_json(csv_path, output_json_path1,output_json_path2, question_column='原始题干'):
    """
    将CSV文件中的题干列转换为指定JSON格式
    
    参数:
        csv_path: CSV文件路径
        output_json_path: 输出JSON文件路径
        question_column: 题干列名，默认为'题干'
    """
    # 导入HTML清洗所需模块
    import html_utils as hu
    
    # 读取CSV文件
    df = pd.read_csv(csv_path)
    
    # 构建JSON数据
    result = []
    result1 = []
    all_question = []
    for _, row in df.iterrows():
        if question_column in row:
            # 清洗HTML标签
            text = str(row[question_column])
            clean_text = hu.clean_html(text,False,True,False)
            # clean_text = BeautifulSoup(text, "html.parser").get_text()
            clean_text = annotate_numbers(clean_text)
            if clean_text.strip() == "" or len(clean_text)<10 or clean_text in all_question:  # 检查清洗后的文本是否为空
                continue  # 如果为空，跳过当前行
            all_question.append(clean_text)
            result.append({
                "doc_token": clean_text,  # 使用清洗后的文本
                "doc_label": []  # 空标签列表
            })
            ss = [kp.strip() for kp in str(row['知识点名称']).split(';') if kp.strip()]
            
            result1.append({
                "doc_token": clean_text,  # 使用清洗后的文本
                "doc_label": ss
            })
    
    # 保存为JSON文件
    with open(output_json_path1, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    with open(output_json_path2, 'w', encoding='utf-8') as f:
        json.dump(result1, f, ensure_ascii=False, indent=2)
    
    print(f"转换完成，结果已保存到文件")


def analyze_label_predictions(excel_path1, excel_path2):
    """
    分析标签预测结果并构建完整路径
    
    参数:
        excel_path1: 包含知识树的Excel文件路径
        excel_path2: 包含预测结果的Excel文件路径(结果也将保存到此文件)
    """
    # 1. 读取知识树并构建节点映射
    df_tree = pd.read_excel(excel_path1, sheet_name='Sheet1')
    nodes = {row['ID']: {'name': row['名称'], 'parent_id': row['父级ID'], 'level': row['节点类型']} 
             for _, row in df_tree.iterrows()}
    
    # 2. 读取预测结果
    df_pred = pd.read_excel(excel_path2, sheet_name='Sample_Contents')  # 假设第三分页名为Sheet3
    
    # 3. 为每个标签构建完整路径
    def get_full_path(node_id):
        path = []
        current = nodes.get(node_id)
        while current:
            path.insert(0, f"{current['name']}")
            current = nodes.get(current['parent_id'])
        return ' -> '.join(path)
    
    # 4. 添加路径列
    df_pred['True_Full_Path'] = df_pred['True_Label_Name'].apply(
        lambda x: get_full_path(next((k for k, v in nodes.items() if v['name'] == x), None)))
    df_pred['Pred_Full_Path'] = df_pred['Pred_Label_Name'].apply(
        lambda x: get_full_path(next((k for k, v in nodes.items() if v['name'] == x), None)))
    
    # 5. 保存结果到原Excel文件的新sheet中
    with pd.ExcelWriter(excel_path2, engine='openpyxl', mode='a') as writer:  
        df_pred.to_excel(writer, sheet_name='分析结果', index=False)
    
    print(f"分析结果已保存到 {excel_path2} 的'分析结果'工作表中")


def split_dataset_by_knowledge(csv_path, train_output, valid_output, test_output, ratio=(0.8, 0.1, 0.1)):
    """
    按知识点名称分类并按比例分割数据集，确保每个类别在三个集合中都有数据
    
    参数:
        csv_path: 输入CSV文件路径
        train_output: 训练集输出文件路径
        valid_output: 验证集输出文件路径
        test_output: 测试集输出文件路径
        ratio: 分割比例，默认为(0.8, 0.1, 0.1)
    """
    # 读取CSV文件
    df = pd.read_csv(csv_path,encoding="gb18030")
    
    # 按知识点名称分组
    grouped = df.groupby('知识点名称')
    
    # 初始化三个数据集
    train_data = []
    valid_data = []
    test_data = []
    
    # 对每个知识点组进行分割
    for name, group in grouped:
        # 打乱数据
        shuffled = group.sample(frac=1, random_state=42)
        total = len(shuffled)
        
        # 确保每个类别至少有3条数据
        if total < 3:
            print(f"警告: 知识点 '{name}' 只有 {total} 条数据，无法保证三个集合都有数据")
            continue
            
        # 计算分割点
        train_end = max(1, int(total * ratio[0]))  # 至少1条
        valid_end = train_end + max(1, int(total * ratio[1]))  # 至少1条
        
        # 分割数据
        train_data.append(shuffled.iloc[:train_end])
        valid_data.append(shuffled.iloc[train_end:valid_end])
        test_data.append(shuffled.iloc[valid_end:])
    
    # 合并各组数据
    train_df = pd.concat(train_data)
    valid_df = pd.concat(valid_data)
    test_df = pd.concat(test_data)
    
    # 保存到文件
    train_df.to_csv(train_output, index=False)
    valid_df.to_csv(valid_output, index=False)
    test_df.to_csv(test_output, index=False)
    
    print(f"数据集已分割并保存到: {train_output}, {valid_output}, {test_output}")


import json

def convert_txt_to_json(input_file, output_file):
    """
    将txt文件中的每一行转换为"知识点": "知识点"形式的JSON文件
    
    参数:
        input_file: 输入txt文件路径
        output_file: 输出json文件路径
    """
    # 读取txt文件
    with open(input_file, 'r', encoding='utf-8') as f:
        knowledge_points = [line.strip() for line in f if line.strip()]
    
    # 构建字典 {"知识点": "知识点"}
    knowledge_dict = {point: point for point in knowledge_points}
    
    # 保存为JSON文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(knowledge_dict, f, ensure_ascii=False, indent=2)
    
    print(f"JSON文件已保存到 {output_file}")


def build_knowledge_tree_and_add_path(excel1_path, excel2_path):
    """
    读取 excel1 文件构建知识树，读取 excel2 文件获取知识点 ID，
    根据 ID 反推知识树路径并保存到 excel2 的最后一列

    参数:
        excel1_path: 用于构建知识树的 Excel 文件路径
        excel2_path: 获取知识点 ID 并添加路径的 Excel 文件路径
    """
    # 读取 excel1 文件构建知识树
    df_tree = pd.read_excel(excel1_path)
    nodes = {}
    # 构建节点字典
    for _, row in df_tree.iterrows():
        node = {
            'id': row['ID'],
            'name': row['名称'],
            'parent_id': row['父级ID'],
            'children': []
        }
        nodes[row['ID']] = node

    # 构建树结构
    for node in nodes.values():
        if pd.notna(node['parent_id']):
            parent = nodes.get(node['parent_id'])
            if parent:
                parent['children'].append(node)

    # 定义反推路径的函数
    def get_path(node_id):
        path = []
        current = nodes.get(node_id)
        while current:
            path.insert(0, current['name'])
            current = nodes.get(current['parent_id'])
        return ' -> '.join(path)

    # 读取 excel2 文件
    df_target = pd.read_excel(excel2_path)

    # 假设 excel2 中知识点 ID 所在列名为 '知识点ID'，你可以根据实际情况修改
    df_target['知识树路径'] = df_target['知识点ID'].apply(lambda x: get_path(x) if x in nodes else '')

    # 保存修改后的 excel2 文件
    df_target.to_excel(excel2_path, index=False)
    print(f"知识树路径已添加到 {excel2_path} 的最后一列")

# 0628 知识点路径知识树构建
def complete_knowledge_path(json_path, excel_path, output_path):
    """
    根据知识树补充JSON文件中的知识点路径
    
    参数:
        json_path: 输入的JSON文件路径
        excel_path: 知识树Excel文件路径
        output_path: 输出JSON文件路径
    """
    # 1. 读取知识树并构建节点映射
    df_tree = pd.read_excel(excel_path)
    nodes = {}
    name_to_node = {}  # 名称到节点的映射
    
    # 构建节点字典
    for _, row in df_tree.iterrows():
        node_name = row['名称'].strip()
        node = {
            'name': node_name,
            'parent_name': row['父级名称'],
            'level': row['节点层级'],
            'children': []
        }
        nodes[node_name] = node
        name_to_node[node_name] = node
    
    # 构建树结构
    for node in nodes.values():
        if pd.isna(node['parent_name']):
            continue  # 根节点不需要处理
        parent = nodes.get(node['parent_name'])
        if parent:
            parent['children'].append(node)
    
    # 2. 读取JSON文件
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 3. 定义获取完整路径的函数
    def get_complete_path(node_name):
        """获取从根节点到当前节点，再到最左侧叶子节点的完整路径"""
        # node_name = node_name[]
        node = name_to_node.get(node_name)
        if not node:
            return []
        
        # 获取从当前节点到根节点的路径
        path = []
        current = node
        while current:
            path.insert(0, current['name'])
            current = nodes.get(current['parent_name'])
        
        # 如果不是叶子节点，补充最左侧路径
        if node['children']:
            left_most = node
            while left_most['children']:
                left_most = left_most['children'][0]
                path.append(left_most['name'])
        
        return path
    
    # 4. 补充知识点路径
    for item in data:
        original_labels = item.get('doc_label', [])
        if not original_labels:
            continue
            
        # 获取第一个知识点的完整路径
        complete_path = get_complete_path(original_labels[0])
        if complete_path:
            item['doc_label'] = complete_path
    
    # 5. 保存结果
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"知识点路径补充完成，结果已保存到 {output_path}")


import pandas as pd
import torch
import os
from collections import defaultdict

def build_level_mappings(excel_path, label_dir, output_dir):
    """
    构建知识树层级映射关系并保存为pt文件

    参数:
        excel_path: 知识树Excel文件路径
        label_dir: 包含labelN.txt的目录路径
        output_dir: 输出目录路径
    """
    # 1. 读取Excel文件构建知识树
    df = pd.read_excel(excel_path)

    # 2. 动态获取所有labelN.txt的N
    label_files = [f for f in os.listdir(label_dir) if re.match(r'label\d+\.txt$', f)]
    levels = sorted([int(re.findall(r'\d+', f)[0]) for f in label_files])

    # 3. 读取各层级label文件并构建名称到索引的映射
    level_to_names = {}
    for level in levels:
        label_file = os.path.join(label_dir, f'label{level}.txt')
        with open(label_file, 'r', encoding='utf-8') as f:
            names = [line.strip() for line in f if line.strip()]
            level_to_names[level] = names

    # 4. 构建层级映射关系
    mappings = {}

    parent_to_children = defaultdict(list)
    for _, row in df.iterrows():
        if pd.notna(row['父级ID']):
            parent_to_children[str(row['父级ID'])].append(row)

    for i in range(len(levels) - 1):
        parent_level = levels[i]
        child_level = levels[i + 1]
        if parent_level not in level_to_names or child_level not in level_to_names:
            continue

        mapping = defaultdict(list)
        parent_names = level_to_names[parent_level]
        child_names = level_to_names[child_level]

        parent_id_to_idx = {kid: idx for idx, kid in enumerate(parent_names)}
        child_id_to_idx = {kid: idx for idx, kid in enumerate(child_names)}

        for parent_id in parent_names:
            parent_idx = parent_id_to_idx.get(parent_id)
            if parent_idx is None:
                continue
            for child_row in parent_to_children.get(parent_id, []):
                child_id = str(child_row['ID'])
                if child_id in child_id_to_idx:
                    child_idx = child_id_to_idx[child_id]
                    mapping[parent_idx].append(child_idx)

        if mapping:
            mappings[f"level{parent_level}_to_level{child_level}"] = dict(mapping)

    # 5. 保存为pt文件和json文件
    os.makedirs(output_dir, exist_ok=True)
    for name, mapping in mappings.items():
        torch.save(mapping, os.path.join(output_dir, f"{name}.pt"))
        with open(os.path.join(output_dir, f"{name}.json"), 'w', encoding='utf-8') as f:
            json.dump(mapping, f, ensure_ascii=False, indent=2)

    print(f"层级映射关系已保存到 {output_dir}，共生成{len(mappings)}个pt文件")



if __name__ == "__main__":
    # 1.处理从数据库导出的知识树excel格式，通过反推实现唯一树 调用get_knowledge_tree0702 main main1


    # excel1 = "d:/TACL_data_process/补全czsx_knowledge_tree3.xlsx"
    # excel2 = "d:/TACL_data_process/多知识点题目.xlsx"
    # build_knowledge_tree_and_add_path(excel1, excel2)

    # convert_txt_to_json(
    #     input_file='d:/TACL_data_process/补全knowledge_levels0613/all_id.txt',
    #     output_file='d:/TACL_data_process/补全knowledge_levels0613/knowledge_discribe.json')

    # 补全知识点到最深层级
    # complete_knowledge_tree("D:\\TACL_data_process\\math2\\math2_knowledge_tree.xlsx","D:\\TACL_data_process\\math2\\fix_math2_knowledge_tree.xlsx")

    # split_dataset_by_knowledge("D:\\TACL_data_process\\math2\\math2_questions.csv","D:\\TACL_data_process\\math2\\wos_train.csv","D:\\TACL_data_process\\math2\\wos_val.csv","D:\\TACL_data_process\\math2\\wos_test.csv")
    
    # 构建知识树并补充路径
    # complete_knowledge_path(
    #     json_path="d:/TACL_data_process/wos_test_single_path.json",
    #     excel_path="d:/TACL_data_process/补全知识树结构.xlsx", 
    #     output_path="d:/TACL_data_process/wos_test_single_path_0628.json"
    # )
    # complete_knowledge_path(
    #     json_path="d:/TACL_data_process/wos_train_single_path.json",
    #     excel_path="d:/TACL_data_process/补全知识树结构0628.xlsx", 
    #     output_path="d:/TACL_data_process/wos_train_single_path_0628.json"
    # )


    # 获取保存各个层级知识点
    # save_knowledge_by_level(
    #     excel_path='/home/<USER>/ZhouSQ/DCX/TACL-chinese1/TACL2024/DCL/TACL/Chinese1/knowledgeTree_Chinese1.xlsx',
    #     output_dir='/home/<USER>/ZhouSQ/DCX/TACL-chinese1/TACL2024/DCL/TACL/Chinese1/knowledgeTree_Chinese1_levels/'
    # )

    # 0702
    build_level_mappings(
        excel_path='/home/<USER>/ZhouSQ/DCX/TACL-chinese1/TACL2024/DCL/TACL/Chinese1/knowledgeTree_Chinese1.xlsx',
        label_dir='/home/<USER>/ZhouSQ/DCX/TACL-chinese1/TACL2024/DCL/TACL/Chinese1/knowledgeTree_Chinese1_levels_num',
        output_dir='/home/<USER>/ZhouSQ/DCX/TACL-chinese1/TACL2024/DCL/TACL/Chinese1/level_index_mappings_num'
    )

    # 切分数据集 
    # split_dataset_by_knowledge('D:\TACL_data_process\math2\math2_all_questions.csv', # type: ignore # type: ignore
    #     'D:\TACL_data_process\math2\wos_train.csv', # type: ignore
    #     'D:\TACL_data_process\math2\wos_val.csv', # type: ignore # type: ignore # type: ignore # type: ignore # type: ignore
    #     'D:\TACL_data_process\math2\wos_test.csv'
    # )

    # build_knowledge_tree_and_convert_json_by_id(
    #     excel_path='D:\TACL_data_process\补全czsx_knowledge_tree3.xlsx',
    #     csv_path='D:\TACL_data_process\多知识点题目.xlsx',
    #     output_path='D:\TACL_data_process\wos_train1.json'
    # )
    # build_knowledge_tree_and_convert_json(
    #     excel_path='D:\\TACL_data_process\\math2\\fix_math2_knowledge_tree.xlsx',
    #     csv_path='D:\\TACL_data_process\\math2\\wos_train.csv',
    #     output_path='D:\\TACL_data_process\\math2\\wos_train.json'
    # )
    # build_knowledge_tree_and_convert_json(
    #     excel_path='D:\\TACL_data_process\\math2\\fix_math2_knowledge_tree.xlsx',
    #     csv_path='D:\\TACL_data_process\\math2\\wos_test.csv',
    #     output_path='D:\\TACL_data_process\\math2\\wos_test.json'
    # )
    # build_knowledge_tree_and_convert_json(
    #     excel_path='D:\\TACL_data_process\\math2\\fix_math2_knowledge_tree.xlsx',
    #     csv_path='D:\\TACL_data_process\\math2\\wos_val.csv',
    #     output_path='D:\\TACL_data_process\\math2\\wos_val.json'
    # )

    # build_knowledge_tree_and_convert_json(
    #     excel_path='D:\\TACL_data_process\\xiaoshu\\fix_math1_knowledge_tree_0703.xlsx',
    #     csv_path='D:\\TACL_data_process\\xiaoshu\\math1_val.csv',
    #     output_path='D:\\TACL_data_process\\xiaoshu\\wos_val.json'
    # )

    # build_knowledge_tree_and_convert_json(
    #     excel_path='D:\TACL_data_process\补全czsx_knowledge_tree3.xlsx',
    #     csv_path='D:\TACL_data_process\wos_val.csv',
    #     output_path='D:\TACL_data_process\wos_val.json'
    # )

    # build_knowledge_tree_and_convert_json(
    #     excel_path='d:\\菁优数据处理\\jy补全知识树0609.xlsx',
    #     json_path='d:\\菁优数据处理\\jy_train_questions_BERT.json',
    #     output_path='d:\\菁优数据处理\\wos_train.json'
    # )
    # build_knowledge_tree_and_convert_json(
    #     excel_path='d:\\菁优数据处理\\jy补全知识树0609.xlsx',
    #     json_path='d:\\菁优数据处理\\jy_valid_questions_BERT.json',
    #     output_path='d:\\菁优数据处理\\wos_valid.json'
    # )
    pass