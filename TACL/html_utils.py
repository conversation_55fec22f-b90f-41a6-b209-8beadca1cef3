import re

def find_latex(content: str) -> list[str]:
    try:
        return re.findall(r'\$(.+?)\$', content)
    except re.error:
        raise ValueError("latex匹配失败")

def convert_to_pinyin_format(text: str) -> str:
    return re.sub(r'<strong class="pinyin.*?tianzige.*?data-content-before=".*?<img.*?tianzige3.png.*?</strong>', '[pinyin]', text, flags=re.DOTALL)

def convert_to_tianzige_format(text: str) -> str:
    def repl(match):
        m = re.search(r'data-content-before="([^"]*)"', match.group(0))
        return f"[tianzige={m.group(1)}]" if m and m.group(1) else "[tianzige]"
    return re.sub(r'<strong class="pinyin.*?tianzige.*?data-content-before=".*?<img.*?src=".*?</strong>', repl, text, flags=re.DOTALL)

def clean_html(text: str, keep_img=True, keep_latex=True, keep_punctuation=True) -> str:
    text = re.sub(r'<(table|tr|tbody).*?>', '', text)
    text = re.sub(r'data:image/[^;]+;base64,[^"]+', '', text)

    if keep_latex:
        text = re.sub(r'<img[^>]*data-latex="(.*?)"[^>]*/>', r'$$\1$', text)
    else:
        text = re.sub(r'<img[^>]*data-latex="(.*?)"[^>]*/>', '', text)

    text = re.sub(r'<img[^>]*src=".*?sixian.png"[^>]*/>', '', text)

    text = convert_to_pinyin_format(text)
    text = convert_to_tianzige_format(text)

    if keep_img:
        text = re.sub(r'<img.*?src=".*?/([\w]+\.(?:png|jpg|gif|tif))".*?/>', r'\1', text)
    else:
        text = re.sub(r'<img.*?src=".*?/([\w]+\.(?:png|jpg|gif|tif))".*?/>', '', text)

    text = re.sub(r'<img.*?src=""[^>]*/>', '', text)

    if keep_latex:
        text = re.sub(r'<sub.*?>(.*?)</sub>', r'$_{\1}$', text)
        text = re.sub(r'<sup.*?>(.*?)</sup>', r'$^{\1}$', text)
    else:
        text = re.sub(r'<sub.*?>(.*?)</sub>', '', text)
        text = re.sub(r'<sup.*?>(.*?)</sup>', '', text)

    text = re.sub(r'<p.*?>([\s\S]*?)</p>', r'\1', text)

    # Remove all span tags iteratively
    for _ in range(10):
        if "<span" in text:
            text = re.sub(r'<span.*?>(.*?)</span>', r'\1', text)
        else:
            break

    text = re.sub(r'<div.*?>(.*?)</div>', r'\1', text)
    text = re.sub(r'<font.*?>(.*?)</font>', r'\1', text)
    text = re.sub(r'(<strong.*?>)*(.*?)(</strong>)*', r'\2', text)
    text = re.sub(r'<b.*?>(.*?)</b>', r'\1', text)
    text = re.sub(r'<br.*?>', '', text)
    text = re.sub(r'<h\d.*?>(.*?)</h\d>', r'\1', text)
    text = re.sub(r'<ol.*?><li.*?>(.*?)</li></ol>', r'\1', text)

    # Remove unnecessary characters
    text = re.sub(r"[ \u3000\t\n\r]|&nbsp;", '', text)
    text = text.replace("\\angle", "∠")

    text = re.sub(r'【.*?】', '', text)
    text = re.sub(r'_', '', text)
    text = text.replace("。", "")
    text = text.replace("<p>", "")
    text = text.replace("&nbsp;", "")

    if not keep_punctuation:
        text = re.sub(r'[.，,!?？"“”]', '', text)

    text = re.sub(r'[（）()]', '', text)
    text = re.sub(r'<.*?>', '', text)
    text = re.sub(r'<imgstyle.*?src="', '', text)

    # 清除形式为纯田字格的内容
    text = re.sub(r'^(\[tianzige\])+$', '', text)

    return text
