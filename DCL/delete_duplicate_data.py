import json
import os

# 要处理的所有 JSON 文件路径
original_data_paths = [
    '/home/<USER>/ZhouSQ/DCX/TACL-chinese1/DCL/dataset/WebOfScience/wos_test.json',
    '/home/<USER>/ZhouSQ/DCX/TACL-chinese1/DCL/dataset/WebOfScience/wos_train.json',
    '/home/<USER>/ZhouSQ/DCX/TACL-chinese1/DCL/dataset/WebOfScience/wos_val.json'
]

# 处理每个文件
for original_data_path in original_data_paths:
    # 获取原始文件名和路径
    file_name, file_extension = os.path.splitext(os.path.basename(original_data_path))
    unique_data_path = os.path.join(os.path.dirname(original_data_path), f"{file_name}_unique{file_extension}")

    # 读取原始的 JSON 文件
    with open(original_data_path, 'r', encoding='utf-8') as infile:
        data = json.load(infile)

    # 使用集合去重
    unique_data = []
    seen = set()

    def deep_convert(value):
        """递归地将列表转换为元组，将字典保持不变"""
        if isinstance(value, list):
            return tuple(deep_convert(item) for item in value)  # 递归转换列表中的每个元素
        elif isinstance(value, dict):
            return tuple((k, deep_convert(v)) for k, v in sorted(value.items()))  # 递归转换字典
        else:
            return value

    def dict_to_tuple(d):
        """将字典转换为不可变的元组"""
        return tuple((k, deep_convert(v)) for k, v in sorted(d.items()))

    # 去重操作
    for entry in data:
        entry_tuple = dict_to_tuple(entry)
        if entry_tuple not in seen:
            seen.add(entry_tuple)
            unique_data.append(entry)

    # 将去重后的数据写入新的 JSON 文件
    with open(unique_data_path, 'w', encoding='utf-8') as outfile:
        json.dump(unique_data, outfile, ensure_ascii=False, indent=4)

    # 输出去重后的文件路径
    print(f"去重后的数据已保存到 {unique_data_path}")
