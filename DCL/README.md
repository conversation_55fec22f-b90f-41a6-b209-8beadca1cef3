# DCL 分层文本分类使用指南

## 📁 文件说明

```
DCL/
├── train.py                 # 基础模型训练
├── embedding.py             # 嵌入训练（需要train.py的权重）
├── topk.py                  # Top-K训练（可选）
├── predict.py               # 单条/批量预测
├── get_detail_prediction.py # 详细预测分析
├── evaulate.py              # 模型评估
├── TSNE.py                  # t-SNE可视化
├── confidence_threshold_analysis.py # 置信度阈值分析
├── delete_duplicate_data.py # 数据预处理
├── dataset/                 # 数据集目录
└── models/                  # 模型定义文件
```

## 🔄 完整使用流程

### 步骤1: 基础训练 (train.py)

**作用**: 训练基础的分层分类模型

**需要修改的路径**:
```python
# 第47行: 模型路径
parser.add_argument("--model_name_or_path", default='/home/<USER>/ZhouSQ/DCX/TACL-chinese1/chinese-roberta-wwm-ext')

# 第48行: 结果保存路径
parser.add_argument("--result_file", default="/home/<USER>/ZhouSQ/DCX/TACL-chinese1/result/few_shot_train.txt")
```

**运行命令**:
```bash
python train.py --dataset=wos --batch_size=64 --max_epochs=100
```

**自动生成的文件夹和文件**:
- 📁 `template/` - 存放prompt模板文件
  - `wos_mask_template.txt` - WebOfScience数据集的mask模板
  - `manual_template.txt` - 手动模板（如果不使用multi_mask）
- 📁 `ckpts/` - 存放训练好的模型权重
  - `时间戳-lr-学习率-lm_training-1-lm_alpha-0.7-batch_size-批次大小-macro.ckpt`
  - `时间戳-lr-学习率-lm_training-1-lm_alpha-0.7-batch_size-批次大小-micro.ckpt`
- 📁 `runs/train/时间戳-参数/` - TensorBoard日志文件
- 📁 `result/` - 训练结果文本文件

---

### 步骤2: 嵌入训练 (embedding.py)

**作用**: 基于步骤1的权重，训练文本嵌入表示

**需要修改的路径**:
```python
# 第42行: 关键! 修改load_state_dict_path为步骤1生成的权重文件
load_state_dict_path = '/home/<USER>/ZhouSQ/DCX/TACL-chinese1/ckpts/2025-08-06_17-32-28-lr-3e-05-lm_training-1-lm_alpha-0.7-batch_size-64-macro.ckpt'

# 第47行: 模型路径（与train.py保持一致）
parser.add_argument("--model_name_or_path", default='/home/<USER>/ZhouSQ/DCX/TACL-chinese1/chinese-roberta-wwm-ext')

# 第48行: 结果文件路径
parser.add_argument("--result_file", default="/home/<USER>/ZhouSQ/DCX/TACL-chinese1/result/few_shot_train.txt")
```

**运行命令**:
```bash
python embedding.py
```

**自动生成的文件夹和文件**:
- 📁 `template/` - 复用train.py生成的模板文件
- 📁 `ckpts/` - 复用，不会新生成权重文件
- 📁 `result/` - 追加训练结果到文本文件
- 📄 `_30shot_none_171_embed_doc_0.pkl` - **重要!** 嵌入文件，后续predict.py需要用到
- 📄 `_30shot_none_171_embed_label_0.pkl` - 标签嵌入文件

---

### 步骤3: 预测使用

#### 3.1 单条预测 (predict.py)

**需要修改的路径**:
```python
# 第497行左右: 模型权重路径（步骤1的输出）
model_ckpt_path = "/home/<USER>/ZhouSQ/DCX/TACL-chinese1/ckpts/your_model.ckpt"

# 第498行左右: 嵌入文件路径（步骤2的输出）
embedding_pkl_path = "/home/<USER>/ZhouSQ/DCX/TACL-chinese1/_30shot_none_171_embed_doc_0.pkl"
```

**自动生成的文件夹和文件**:
- 📁 `template/` - 如果不存在会自动创建并生成默认模板

**使用方法**:
```python
from predict import HierarchicalTextClassifier

classifier = HierarchicalTextClassifier(model_ckpt_path, embedding_pkl_path)
result = classifier.predict("输入文本", topk=3)
```

#### 3.2 详细预测分析 (get_detail_prediction.py)

**需要修改的路径**:
```python
# 第351行左右: 模型路径
model_path = "/home/<USER>/ZhouSQ/DCX/TACL-chinese1/ckpts/your_model.ckpt"

# 第352行左右: 嵌入路径
embedding_path = "/home/<USER>/ZhouSQ/DCX/TACL-chinese1/_30shot_none_171_embed_doc_0.pkl"

# 第339行左右: 测试数据路径
data_file = "/home/<USER>/ZhouSQ/DCX/TACL-chinese1/dataset/WebOfScience/wos_test.json"
```

#### 3.3 Top-K训练 (topk.py) - 可选

**作用**: 进一步优化Top-K预测性能

**需要修改的路径**: 与embedding.py类似，需要指定基础模型权重路径

**自动生成的文件夹和文件**:
- 📁 `template/` - 复用已有模板文件
- 📁 `ckpts/` - 复用，不会新生成权重文件

---

### 步骤4: 模型评估 (evaulate.py)

**需要修改的路径**:
```python
# 模型权重路径
model_path = "/home/<USER>/ZhouSQ/DCX/TACL-chinese1/ckpts/your_model.ckpt"
```

## 📂 自动生成的文件夹详解

### 🗂️ template/ 文件夹
**生成时机**: train.py、embedding.py、topk.py首次运行时
**作用**: 存放prompt模板文件，定义输入文本的格式化方式
**文件内容**:
- `manual_template.txt` - 手动模板: `It was 1 level: {"mask"} 2 level: {"mask"} 3 level: {"mask"}. {"placeholder": "text_a"}`
- `wos_mask_template.txt` - WebOfScience数据集专用模板

### 🗂️ ckpts/ 文件夹
**生成时机**: train.py训练过程中
**作用**: 存放训练好的模型权重文件
**文件命名规律**: `时间戳-lr-学习率-lm_training-1-lm_alpha-0.7-batch_size-批次大小-macro.ckpt`
**示例**: `2025-08-07_15-29-38-lr-3e-05-lm_training-1-lm_alpha-0.7-batch_size-64-macro.ckpt`

### 🗂️ runs/train/ 文件夹
**生成时机**: train.py运行时
**作用**: TensorBoard训练日志，可视化训练过程
**查看方式**: `tensorboard --logdir=runs/train/`

### 🗂️ result/ 文件夹
**生成时机**: train.py、embedding.py运行结束时
**作用**: 存放训练结果的文本记录
**文件内容**: 超参数、性能指标、训练时间等

### 📄 嵌入文件 (.pkl)
**生成时机**: embedding.py运行结束时
**命名规律**: `_30shot_none_171_embed_doc_0.pkl` (参数会变化)
**作用**: 存储文本和标签的嵌入向量，供predict.py使用

## ⚠️ 重要注意事项

### 路径依赖关系
```
train.py → 生成权重文件 → embedding.py → 生成pkl文件 → predict.py/get_detail_prediction.py
```

### 常见路径配置错误
1. **embedding.py**: 忘记修改第42行 `load_state_dict_path` 为train.py的输出权重
2. **predict.py**: 忘记修改 `embedding_pkl_path` 为embedding.py的输出pkl文件
3. **模型路径**: 各个脚本中的 `model_name_or_path` 要保持一致
4. **文件不存在**: 确保权重文件和pkl文件路径正确且文件存在

### 文件生成规律
- **权重文件**: `ckpts/时间戳-lr-学习率-参数信息.ckpt`
- **嵌入文件**: `_参数shot_none_171_embed_doc_0.pkl`
- **模板文件**: `template/数据集名_mask_template.txt`

## 🔧 其他脚本说明

### TSNE.py
**作用**: 可视化文本嵌入的t-SNE降维图
**依赖**: 需要embedding.py生成的pkl文件
**输出**: t-SNE可视化图片

### confidence_threshold_analysis.py
**作用**: 分析不同置信度阈值下的模型性能
**依赖**: 需要模型权重文件和嵌入pkl文件
**输出**: 最佳置信度阈值建议

### delete_duplicate_data.py
**作用**: 数据预处理，删除数据集中的重复样本
**使用时机**: 训练前的数据清洗

## 📝 快速检查清单

使用前请确认：
- [ ] train.py中第47行模型路径正确
- [ ] embedding.py中第42行load_state_dict_path指向train.py的输出权重
- [ ] predict.py中的model_ckpt_path和embedding_pkl_path都正确设置
- [ ] dataset/WebOfScience/目录存在且包含数据文件
- [ ] 有足够的磁盘空间存储生成的文件
- [ ] GPU内存足够运行训练
